[{"duration": 0.013908999972045422, "eventName": "sdk:pre-test", "startTime": 5726676.340342, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}, {"duration": 0.005072999745607376, "eventName": "sdk:post-test", "startTime": 5796327.575189, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}, {"duration": 0.01464999932795763, "eventName": "sdk:pre-test", "startTime": 6359871.957534, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}, {"duration": 0.004782000556588173, "eventName": "sdk:post-test", "startTime": 6363459.506339, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}, {"duration": 0.015358999371528625, "eventName": "sdk:pre-test", "startTime": 6395781.90887, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}, {"duration": 0.004554999992251396, "eventName": "sdk:post-test", "startTime": 6437396.324258, "success": true, "worker": 1, "failure": null, "details": null, "entryType": "measure", "platform": 0, "command": null, "testName": null, "hookType": "", "cli": false}]