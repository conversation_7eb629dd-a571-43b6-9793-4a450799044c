<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>LenskartAppFramework</groupId>
	<artifactId>LenskartAppFramework</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	
	
		<build>
		<pluginManagement>
			<plugins>


				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>3.5.1</version>
					
					<configuration>
					
						<suiteXmlFiles>
						
							<suiteXmlFile>${suiteXmlFiles}</suiteXmlFile>

						</suiteXmlFiles>

					</configuration>
					
				</plugin>


				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.13.0</version>
					<!-- <configuration>
					
						<suiteXmlFiles>
						
							<suiteXmlFile>testng.xml</suiteXmlFile>

						</suiteXmlFiles>

					</configuration> -->
				</plugin>


			</plugins>
		</pluginManagement>
	</build>
	
	

	<dependencies>
		<!-- https://mvnrepository.com/artifact/org.testng/testng -->
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>6.14.3</version>
			<!-- <version>7.3.0</version> -->
		</dependency>

		<!-- https://mvnrepository.com/artifact/io.appium/java-client -->
		<dependency>
			<groupId>io.appium</groupId>
			<artifactId>java-client</artifactId>
			<version>7.3.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-java -->
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>3.141.59</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.json/json -->
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20190722</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-server -->
		<!-- <dependency> <groupId>org.seleniumhq.selenium</groupId> <artifactId>selenium-server</artifactId> 
			<version>3.141.59</version> </dependency> -->
		<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-server -->
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-server</artifactId>
			<version>3.5.3</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.aventstack/extentreports -->
		<dependency>
			<groupId>com.aventstack</groupId>
			<artifactId>extentreports</artifactId>
			<version>4.1.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.aventstack/extentreports-testng-adapter -->
		<!--<dependency> <groupId>com.aventstack</groupId> <artifactId>extentreports-testng-adapter</artifactId> 
			<version>1.0.7</version> </dependency> -->



		<!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.16.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-api -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.16.0</version>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>3.8</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.jayway.restassured/rest-assured -->
		<dependency>

			<groupId>com.jayway.restassured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>2.9.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.mashape.unirest/unirest-java -->
		<dependency>
			<groupId>com.mashape.unirest</groupId>
			<artifactId>unirest-java</artifactId>
			<version>1.4.9</version>
		</dependency>

		<!-- 		<dependency>
                    <groupId>com.testinium.deviceinformation</groupId>
                    <artifactId>device-information</artifactId>
                    <version>2.0</version>
                </dependency> -->

                <!-- https://mvnrepository.com/artifact/io.rest-assured/rest-assured -->
		<dependency>
			<groupId>io.rest-assured</groupId>
			<artifactId>rest-assured</artifactId>
			<version>4.3.3</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.0-rc1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.aeonbits.owner/owner -->
		<dependency>
			<groupId>org.aeonbits.owner</groupId>
			<artifactId>owner</artifactId>
			<version>1.0.12</version>
		</dependency>



		<!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.22</version>
			<scope>provided</scope>
		</dependency>



		<dependency>
			<groupId>com.browserstack</groupId>
			<artifactId>browserstack-java-sdk</artifactId>
			<version>1.38.0</version>
		</dependency>





	</dependencies>
	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
	</properties>
	<repositories>
		<repository>
			<id>public</id>
			<name>public</name>
			<url>http://mvn.testinium.com/repository/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>