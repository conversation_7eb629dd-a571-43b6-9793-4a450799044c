{"newAddress": {"HouseNoOrBuildingName": "house test test", "enterRoadNameOrArea": "test road test", "landMark": "testLandMark", "city": "GURUGRAM", "country": "INDIA", "Name": "test lastName", "locality": "test", "phoneNumber": "5000000000", "phoneCode": "+91", "Pincode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "address1": {"addressline1": "test test", "city": "GURUGRAM", "country": "INDIA", "email": "<EMAIL>", "fullName": "Test tester", "gender": "unknown", "lastName": "test", "locality": "test", "phone": "5000000000", "phoneCode": "+91", "postcode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "address2": {"addressline1": "test test", "city": "GURUGRAM", "country": "INDIA", "email": "<EMAIL>", "fullName": "Test tester", "gender": "unknown", "lastName": "test", "locality": "test", "phone": "5000000000", "phoneCode": "+91", "postcode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "sgaddress": {"addressline1": "test Singapore", "city": "Singapore", "country": "SG", "email": "<EMAIL>", "fullName": "test test", "lastName": " test", "locality": "Singapore ", "phone": "88888888", "phoneCode": "+91", "postcode": "569933", "unitNumber": "12345", "saveAsOther": "testers home", "state": "Singapore"}, "address3": {"addressline1": "test test", "city": "Gurgaon", "country": "INDIA", "email": "<EMAIL>", "fullName": "Test tester", "gender": "unknown", "lastName": "test", "locality": "test", "phone": "5000000000", "phoneCode": "+91", "postcode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "address4": {"addressline1": "test test", "city": "Gurgaon", "country": "INDIA", "email": "<EMAIL>", "fullName": "Test tester", "gender": "unknown", "lastName": "test", "locality": "test", "phone": "5000000000", "phoneCode": "+91", "postcode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "addressMobNo": {"addressline1": "test test", "city": "Gurgaon", "country": "INDIA", "email": "<EMAIL>", "fullName": "Test tester", "gender": "unknown", "lastName": "test", "locality": "test", "phone": "5000000000", "phoneCode": "+91", "postcode": "122004", "saveAsOther": "testerHomeTown", "state": "Haryana"}, "addressEmailId": {"addressline1": "test EmailId", "city": "Bangalore", "country": "INDIA", "email": "", "firstName": "Test", "gender": "unknown", "lastName": "EmailId", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "addressInvalidEmailId": {"addressline1": "test EmailId", "city": "Bangalore", "country": "INDIA", "email": "aaa.lenskart.com", "firstName": "Test", "gender": "unknown", "lastName": "EmailId", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "addressName": {"addressline1": "test Name", "city": "Bangalore", "country": "INDIA", "email": "<EMAIL>", "firstName": "", "gender": "unknown", "lastName": "", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "addressLastName": {"addressline1": "test Name", "city": "Bangalore", "country": "INDIA", "email": "<EMAIL>", "firstName": "Last", "gender": "unknown", "lastName": "", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "addressPincode": {"addressline1": "test EmailId", "city": "Bangalore", "country": "INDIA", "email": "<EMAIL>", "firstName": "Test", "gender": "unknown", "lastName": "EmailId", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "", "state": "KARNATAKA"}, "addressAddress": {"addressline1": "", "city": "Bangalore", "country": "INDIA", "email": "<EMAIL>", "firstName": "Test", "gender": "unknown", "lastName": "EmailId", "locality": "test", "phone": "4000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "addressNonGold": {"addressline1": "", "city": "Bangalore", "country": "INDIA", "email": "<EMAIL>", "firstName": "Test", "gender": "unknown", "lastName": "EmailId", "locality": "test", "phone": "2000000000", "phoneCode": "+91", "postcode": "560010", "state": "KARNATAKA"}, "sgstoreaddress": {"addressline1": "Basement 1, #B1-08/09, 107, North Bridge Road, Funan", "city": "Singapore, Singapore", "country": "Singapore, Singapore", "email": "<EMAIL>", "firstName": "Test", "gender": "unknown", "lastName": "T", "locality": "<PERSON><PERSON>", "phone": "Mob. 69701335", "phoneCode": "+91", "postcode": "Singapore - 179105", "state": "KARNATAKA"}, "uaeAddress": {"addressline1": "test", "city": "test", "country": "AE", "email": "<EMAIL>", "firstName": "test", "lastName": " test", "locality": "test ", "phone": "555555555", "phoneCode": "+971", "postcode": "000000", "saveAsOther": "testers home", "state": "test"}, "SaAddress": {"addressline1": "test", "city": "test", "country": "AE", "email": "<EMAIL>", "firstName": "test", "lastName": " test", "locality": "test ", "phone": "555555555", "phoneCode": "+966", "postcode": "000000", "saveAsOther": "testers home", "state": "test"}}