<?xml version="1.0" encoding="UTF-8"?>
<suite parallel="tests" name="Suite">

	<test name="Test1">

		<listeners>
			<listener class-name="com.qa.listeners.TestListener" />
		</listeners>

		<groups>
			<run>
				<!--To run sanity testcases change the name -->
				<!--OR -->
				<!--To run regression testcases change the name :regression -->
				<include name="regression"></include>
			</run>
		</groups>


		<parameter name="loginUser" value="androidUser1" />
		<parameter name="automationRunningPlatform"
				   value="browserStackAndroid" />
		<!-- SA, SG, AE,IN, US -->
		<parameter name="country" value="IN" />
		<!-- android, iOS -->
		<parameter name="platformName" value="android" />
		<!--<parameter name="platformVersion" value="10.0" />
		<parameter name="deviceName"
			value="Samsung Galaxy S20 Plus" />-->

		<parameter name="platformVersion" value="11.0" />
		<parameter name="deviceName"
				   value="OnePlus 9" />

		<classes>

			<class name="com.qa.indiaAndroid.TestCase" />


		</classes>
		
	</test> <!-- Test -->



</suite> <!-- Suite -->