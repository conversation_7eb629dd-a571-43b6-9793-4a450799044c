<?xml version="1.0" encoding="UTF-8"?>
<suite parallel="false" name="Suite">
	<listeners>
		<listener class-name="com.qa.listeners.TestListener" />
	</listeners>

	<groups>
		<run>
			<!--To run sanity testcases change the name :sanity -->
			<!--OR -->
			<!--To run regression testcases change the name :regression -->
			<include name="regression"></include>
		</run>
	</groups>


	<test name="Test">

		<parameter name="loginUser" value="androidUser1" />
		<parameter name="automationRunningPlatform" value="android" />
		<!-- android, iOS emulator-5556 Pixel_3a_API_30 -->
		<parameter name="country" value="IN" />
		<parameter name="platformName" value="android" />
		<!-- <parameter name="platformVersion" value="19" /> -->


		<parameter name="udid" value="5lydmzpba6cudexg" />
		<parameter name="deviceName" value="Redmi Note_8_Pro" />

		<!-- <parameter name="udid" value="RZ8R80TR3JB" /> <parameter name="deviceName" 
			value="samsung" /> -->

		<classes>

			<class name="com.qa.indiaAndroid.TestCase" />


		</classes>
		
	</test> <!-- Test -->


</suite> <!-- Suite -->