package com.qa.testDataProviders;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

import org.testng.annotations.DataProvider;

import com.opencsv.CSVReaderBuilder;
import com.opencsv.enums.CSVReaderNullFieldIndicator;
import com.opencsv.exceptions.CsvException;

public class IndiaDataProveider {
	
	
	@DataProvider(name = "sunglassWithoutPower")
	public static Iterator<String[]> eyeglassesWithoutPower() throws IOException, CsvException {
		List<String[]> list=null;
		
			list = new CSVReaderBuilder(new BufferedReader(new FileReader("src/test/resources/data/IN/orderPlaceDatas/sunglassWithoutPower.csv"))).withSkipLines(1).withFieldAsNull(CSVReaderNullFieldIndicator.BOTH).build().readAll();
			
		return list.iterator();

	}
	
	
}
