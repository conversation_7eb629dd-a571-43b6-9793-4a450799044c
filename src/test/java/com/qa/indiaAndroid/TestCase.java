package com.qa.indiaAndroid;

import org.testng.annotations.Test;

import com.qa.BaseTest;
import com.qa.appPages.AppAddressPage;
import com.qa.driverManagers.DriverManagers;
import com.qa.reports.CustomLogger;
import com.qa.testDataProviders.IndiaDataProveider;
import com.qa.utils.DataLoader;
import com.qa.utils.TestUtils;


import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;

import static org.testng.Assert.assertTrue;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.HashMap;

import org.json.JSONObject;
import org.json.JSONTokener;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.AfterTest;

public class TestCase extends BaseTest{

	//dataProvides
	
	InputStream datais;

	//pageVariable
	AppAddressPage appAddressPage;
	HashMap<String, String> addressData;
	
	@BeforeClass(alwaysRun = true)
	public void beforeClass() throws IOException {


		TestUtils utils = new TestUtils();		
		addressData = DataLoader.getAddressData("newAddress");

		
	}


	@BeforeMethod(alwaysRun = true)
	public void beforeMethod(Method m) throws InterruptedException{

		appAddressPage = new AppAddressPage();
		
		System.out.println("\n" + "starting test: " + " **** " + m.getName() + " ****" + "\n");
	}



	@Test(dataProviderClass = IndiaDataProveider.class, dataProvider = "sunglassWithoutPower",groups = {"sanity", "regression"},priority = 3)
	public void deleteAddress(String searchText, String lensName) {

		launchApp();
		custom_Log( "test sucesss");
	

	}


	@AfterClass(alwaysRun = true)
	public void afterMethod() {
		closeApp();
	}


}
