package com.qa;

import org.testng.annotations.Test;


import io.appium.java_client.ios.IOSElement;


import com.aventstack.extentreports.Status;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.qa.driverManagers.DriverManagers;
import com.qa.expection.FrameWorkExpection;
import com.qa.reports.ExtentReport;
import com.qa.utils.TestUtils;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.FindsByAndroidUIAutomator;
import io.appium.java_client.InteractsWithApps;
import io.appium.java_client.MobileBy;
import io.appium.java_client.MobileElement;
import io.appium.java_client.PerformsTouchActions;
import io.appium.java_client.TouchAction;
import io.appium.java_client.android.Activity;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.AndroidTouchAction;
import io.appium.java_client.android.PushesFiles;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import io.appium.java_client.android.nativekey.PressesKey;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.pagefactory.AppiumFieldDecorator;
import io.appium.java_client.remote.MobileCapabilityType;
import io.appium.java_client.service.local.AppiumDriverLocalService;
import io.appium.java_client.service.local.AppiumServiceBuilder;
import io.appium.java_client.service.local.flags.GeneralServerFlag;
import io.appium.java_client.touch.TapOptions;
import io.appium.java_client.touch.WaitOptions;
import io.appium.java_client.touch.offset.ElementOption;
import io.appium.java_client.touch.offset.PointOption;
import lombok.Getter;

import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeSuite;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Listeners;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;

import java.awt.Point;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.DriverManager;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

import javax.annotation.Nullable;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.ElementClickInterceptedException;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Keys;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.Rectangle;
import org.openqa.selenium.SearchContext;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.interactions.Pause;
import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.Sequence;
import org.openqa.selenium.interactions.PointerInput.Kind;
import org.openqa.selenium.interactions.PointerInput.MouseButton;
import org.openqa.selenium.interactions.PointerInput.Origin;
import org.openqa.selenium.interactions.touch.TouchActions;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.remote.RemoteWebElement;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.AfterTest;

public class BaseTest {

	public static String platformName;
	public static Properties configProperty;

	public static String getPlatformName() {
		return platformName;
	}

	protected BaseTest() {
		PageFactory.initElements(new AppiumFieldDecorator(DriverManagers.getDriver(), Duration.ofSeconds(1)), this);
	}

	@Parameters({"automationRunningPlatform","platformName","udid","deviceName","platformVersion","country","browserName","loginUser"})
	@BeforeTest(alwaysRun = true)
	protected void beforeClass(@Optional String automationRunningPlatform,String platformName,@Optional String udid,String deviceName,@Optional String platformVersion,@Optional String country,@Optional String browserName,@Optional String loginUser) throws IOException {
		this.platformName=platformName;
		System.out.println(country);

		new DriverManagers().startAppDriver(automationRunningPlatform,platformName, udid, deviceName,platformVersion,country,browserName,loginUser);

		configProperty = DriverManagers.getProps();
	}

	@AfterTest(alwaysRun = true)
	protected void afterClass() {
		DriverManagers.getDriver().quit();
	}


	protected void closeTheApp() {
		DriverManagers.getDriver().closeApp();
	}


	protected static void custom_Log(String msg) {
		DriverManagers.getUtils().log().info(msg);
		ExtentReport.getTest().log(Status.INFO, msg);
	}

	/*protected void custom_Log_Error(String msg) {
		DriverManagers.getUtils().log().error(msg);
		ExtentReport.getTest().log(Status.ERROR, msg);
	}*/



	// Wait for element Visibilty
	/*
	 * Explict Wait for element Visibilty with 30sec wait
	 * 
	 * @param MobileElement
	 */
	protected void waitForVisibility(MobileElement element) {

		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(),1); 
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));


		/*	WebDriverWait wait = (WebDriverWait) new WebDriverWait(DriverManagers.getDriver(),1).withMessage(
				() -> "Waited for " + 1 + " seconds but element : " + element + " is not visible");
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));*/
	}

	protected void waitForVisibility(WebElement element) {

		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(),1); 
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));


		/*	WebDriverWait wait = (WebDriverWait) new WebDriverWait(DriverManagers.getDriver(),1).withMessage(
				() -> "Waited for " + 1 + " seconds but element : " + element + " is not visible");
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));*/
	}

	protected void waitForVisibility(By element) {

		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(),1); 
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOfElementLocated(element)));


		/*	WebDriverWait wait = (WebDriverWait) new WebDriverWait(DriverManagers.getDriver(),1).withMessage(
				() -> "Waited for " + 1 + " seconds but element : " + element + " is not visible");
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));*/
	}

	// Wait for element inVisiblity
	/*
	 * Explict Wait for element inVisibilty with 30sec wait
	 * 
	 * @param MobileElement
	 */
	/*protected boolean waitForInvisibilty(MobileElement element) {
		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(), 1);
		return wait.until(ExpectedConditions.refreshed(ExpectedConditions.invisibilityOf(element)));
	}

	/**
	 * Is Element not present
	 * 
	 * @param MobileElement
	 * @return true if not visible
	 */
	/*protected boolean isElementNotPresent(MobileElement element) {
		try {
			return waitForInvisibilty(element);
		} catch (Exception e) {
			return false;
		}
	}*/

	/*
	 * Explict wait method
	 * 
	 * @param MobileElement
	 * @param timeout       InSeconds
	 */
	protected void waitForVisibility(MobileElement element, long timeout) {
		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(), timeout);
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));
		//	wait.ignoring(NoSuchElementException.class, TimeoutException.class);
	}

	protected void waitForVisibility(WebElement element, long timeout) {
		WebDriverWait wait = new WebDriverWait(DriverManagers.getDriver(), timeout);
		wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOf(element)));
		//	wait.ignoring(NoSuchElementException.class, TimeoutException.class);

	}



	/**
	 * Explict wait method with 30 sec wait
	 * 
	 * @param element
	 * @return true
	 */
	protected boolean isElementVisible(MobileElement element, String msg) {
		try {
			waitForVisibility(element);
			custom_Log( msg + " visible");
			return true;
		} catch (Exception e) {
			custom_Log(msg + " not visible");
			return false;
		}
	}

	protected boolean isElementVisible(WebElement element, String msg) {
		try {
			waitForVisibility(element);
			custom_Log( msg + " visible");
			return true;
		} catch (Exception e) {
			custom_Log(msg + " not visible");
			return false;
		}
	}

	protected boolean isElementVisible(MobileElement element, String msg,long time) {
		try {
			waitForVisibility(element,time);
			custom_Log( msg + " visible");
			return true;
		} catch (Exception e) {
			custom_Log(msg + " not visible");
			return false;
		}
	}
	protected boolean isElementVisible(WebElement element, String msg,long time) {
		try {
			waitForVisibility(element,time);
			custom_Log( msg + " visible");
			return true;
		} catch (Exception e) {
			custom_Log(msg + " not visible");
			return false;
		}
	}

	protected boolean isElementVisible(By element, String msg,long time) {
		try {
			waitForVisibility(element);
			custom_Log( msg + " visible");
			return true;
		} catch (Exception e) {
			custom_Log(msg + " not visible");
			return false;
		}
	}

	/*
	 * Click method with explict wait
	 * 
	 * @param MobileElement
	 *
	 */
	protected void clickEvent(MobileElement element) {
		waitForVisibility(element);
		element.click();
	}

	protected void clickEvent(WebElement element) {
		waitForVisibility(element);
		element.click();
	}

	/**
	 * Click method with explict wait
	 * 
	 * @param element
	 * @param msg     logs + Extentreport
	 */
	protected void clickEvent(MobileElement element, String msg) {
		waitForVisibility(element);
		element.click();
		custom_Log(msg);
	}
	protected void clickEvent(MobileElement element, String msg,int time) {
		waitForVisibility(element,time);
		element.click();
		custom_Log(msg);
	}

	protected void clickEvent(WebElement element, String msg) {
		waitForVisibility(element);
		element.click();
		custom_Log(msg);
	}

	protected void clickEvent(WebElement element, String msg,int time) {
		waitForVisibility(element,time);
		element.click();
		custom_Log(msg);
	}


	protected void clickEvent(By element, String msg) {
		waitForVisibility(element);
		DriverManagers.getDriver().findElement(element).click();
		custom_Log(msg);
	}

	protected void clickEvent(By xpath, String msg,int time) {

		MobileElement element = (MobileElement) DriverManagers.getDriver().findElement(xpath);
		waitForVisibility(element,time);	
		element.click();
		custom_Log(msg);
	}

	protected void clickOnAnyElement(List<MobileElement> elements, Predicate<MobileElement> predicate) {
		waitForVisibility(elements.get(0));
		clickEvent(
				elements.stream().parallel().filter(predicate).findAny()
				.orElseThrow(() -> new FrameWorkExpection("Element not found in list")),
				"Clicked on the on element in the list");
	}

	protected void clickOnAnyElement(List<MobileElement> elements) {
		waitForVisibility(elements.get(0));
		clickEvent(
				elements.stream().parallel().findAny()
				.orElseThrow(() -> new FrameWorkExpection("Element not found in list")),
				"Clicked on the on element in the list");

	}

	/*
	 * This method will use android touch actions to tap on the element with wait
	 * 
	 * @param element
	 * @param msg
	 * @param wiat
	 */
	protected void waitAndTap(MobileElement element, String msg, int wait) {
		new AndroidTouchAction((PerformsTouchActions) DriverManagers.getDriver())
		.tap(TapOptions.tapOptions().withElement(ElementOption.element(element)))
		.waitAction(WaitOptions.waitOptions(Duration.ofSeconds(wait))).perform();
		custom_Log( msg + " after waiting for " + wait + " seconds");
	}

	/**
	 * click method for webelement
	 * 
	 * @param element
	 */
	protected void clickWebElement(WebElement element) {
		element.click();
	}

	/**
	 * SendKeys without logs
	 * 
	 * @param element
	 * @param txt     to be entered
	 */
	protected void sendKeysEvent(MobileElement element, String txt) {
		waitForVisibility(element);
		clearText(element);
		element.sendKeys(txt);
	}

	protected void sendKeysEvent(WebElement element, String txt) {
		waitForVisibility(element);
		clearText(element);
		element.sendKeys(txt);
	}

	/*
	 * SendKey with log and extent report
	 * 
	 * @param MobileElement
	 * @param txt           to be entered
	 * @param msg           logs + Extentreport
	 */
	protected void sendKeysEvent(MobileElement e, String txt, String msg) {
		waitForVisibility(e);
		clearText(e);
		clickEvent(e);
		e.sendKeys(txt);
		custom_Log( msg);
	}

	protected void sendKeysEvent(WebElement e, String txt, String msg) {
		waitForVisibility(e);
		clearText(e);
		e.sendKeys(txt);
		custom_Log( msg);
	}

	/*
	 * This method will clear the text present
	 * 
	 * @param MobileElement e
	 */
	protected void clearText(MobileElement e) {
		waitForVisibility(e);
		e.clear();
		// FrameworkLogger.logs(LogType.EXTCONWRT, text + " text cleared");

	}

	protected void clearText(WebElement e) {
		waitForVisibility(e);
		e.clear();
		// FrameworkLogger.logs(LogType.EXTCONWRT, text + " text cleared");

	}

	/**
	 * Get text from the UI and return it as string
	 * 
	 * @param element
	 * @return String
	 */
	protected String getTextOfElement(MobileElement element) {
		waitForVisibility(element);
		return element.getText();
	}


	protected String getTextOfElement(WebElement element) {
		waitForVisibility(element);
		return element.getText();
	}


	/*
	 * Get text from the UI and return it as string
	 * 
	 * @param MobileElement
	 * @param msg           logs + Extentreport
	 * @return String
	 */
	protected String getTextOfElement(MobileElement element, String msg) {
		String txt = null;
		try {

			if(getPlatformName().equals(TestUtils.android())) {
				txt = getAttribute(element, "text");
			}else if(getPlatformName().equals(TestUtils.ios())){
				txt = getAttribute(element, "value");
			}

			custom_Log( msg + ": " + txt);
			return txt;
		} catch (TimeoutException e) {
			throw new FrameWorkExpection("element not found for " + msg);
		}

	}


	protected String getTextOfElement(MobileElement element, String msg,int time) {

		waitForVisibility(element,time);
		String txt = null;
		try {

			if(getPlatformName().equals(TestUtils.android())) {
				txt = getAttribute(element, "text");
			}else if(getPlatformName().equals(TestUtils.ios())){
				txt = getAttribute(element, "value");
			}

			custom_Log( msg + ": " + txt);
			return txt;
		} catch (TimeoutException e) {
			throw new FrameWorkExpection("element not found for " + msg);
		}

	}

	protected String getTextOfElement(WebElement element, String msg) {
		String txt = null;
		try {

			txt = element.getText();

			custom_Log( msg + ": " + txt);
			return txt;
		} catch (TimeoutException e) {
			throw new FrameWorkExpection("element not found for " + msg);
		}

	}

	/**
	 * @param element
	 * @param attribute
	 * @return String
	 */
	protected String getAttribute(MobileElement element, String attribute) {

		waitForVisibility(element);
		return element.getAttribute(attribute);
	}

	/*protected String getAttributeFromText(MobileElement element) {

		waitForVisibility(element);
		if (getPlatformName().equals(TestUtils.ios())) {
			return element.getAttribute("text");
		}else if(getPlatformName().equals(TestUtils.ios())) {
			return element.getAttribute("value");
		}

		return "Attribute value not fount";
	}*/

	protected String getAttribute(WebElement element, String attribute) {

		waitForVisibility(element);
		return element.getAttribute(attribute);
	}

	/**
	 * Click if the element isVisible with logs + extentReport
	 * 
	 * @param element
	 */
	protected void clickIfVisible(MobileElement element, String msg) {
		boolean result = isElementDisplayed(element, 4);
		try {
			if (result) {
				clickEvent(element);
				custom_Log( msg + " element visible and clicked.");
			} else {
				custom_Log( msg + " element not visible.");
			}

		} catch (Exception e) {
			custom_Log( e.toString());

		}
	}

	protected void clickIfVisible(WebElement element, String msg) {
		boolean result = isElementDisplayed(element, 4);
		try {
			if (result) {
				clickEvent(element);
				custom_Log( msg + " element visible and clicked.");
			} else {
				custom_Log( msg + " element not visible.");
			}

		} catch (Exception e) {
			custom_Log( e.toString());

		}
	}

	/**
	 * Method to click if element isVisible at dynamic time
	 * 
	 * @param element
	 * @param timeout
	 */
	protected void clickIfVisible(MobileElement element, String msg,long timeout) {
		boolean result = isElementDisplayed(element, timeout);
		try {
			if (result) {
				clickEvent(element);
				custom_Log( msg + " element visible and clicked.");
			} else {
				custom_Log( msg + " element not visible.");
			}

		} catch (Exception e) {
			custom_Log("Exception Occured while verify present and clicking " + e);
		}

	}

	protected void clickIfVisible(WebElement element, long timeout, String msg) {
		boolean result = isElementDisplayed(element, timeout);
		try {
			if (result) {
				clickEvent(element);
				custom_Log( msg + " element visible and clicked.");
			} else {
				custom_Log( msg + " element not visible.");
			}

		} catch (Exception e) {
			custom_Log("Exception Occured while verify present and clicking " + e);
		}

	}

	/**
	 * Return true if element is displayed
	 * 
	 * @param element
	 * @return true if element is displayed
	 */
	protected static boolean isElementDisplayed(MobileElement element) {
		try {
			if (element.isDisplayed() == true) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	protected static boolean isElementDisplayed(By element) {

		try {
			if (DriverManagers.getDriver().findElement(element).isDisplayed() == true) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	protected static boolean isElementDisplayed(WebElement element) {
		try {
			if (element.isDisplayed() == true) {
				return true;
			} else {
				return false;
			}
		} catch (Exception e) {
			return false;
		}
	}

	/*
	 * Return boolean if element is displayed with log and Extent report
	 * 
	 * @param MobileElement
	 * @param msg           logs + Extentreport
	 * @return true if element is displayed
	 */
	protected boolean isElementDisplayed(MobileElement element, String msg) {

		try {
			if (element.isDisplayed() == true) {
				custom_Log( msg + " displayed");
				return true;
			} else {
				custom_Log( msg + " not displayed");
				return false;
			}
		} catch (Exception e) {
			custom_Log(msg + " not displayed");
			return false;
		}
	}

	protected boolean isElementDisplayed(WebElement element, String msg) {

		try {
			if (element.isDisplayed() == true) {
				custom_Log( msg + " displayed");
				return true;
			} else {
				custom_Log( msg + " not displayed");
				return false;
			}
		} catch (Exception e) {
			custom_Log(msg + " not displayed");
			return false;
		}
	}

	protected boolean isElementDisplayed(By element, String msg, long timeout) {
		try {
			waitForVisibility(element);
			custom_Log( msg + " displayed");
			return true;
		} catch (Exception e) {
			custom_Log( msg + " not displayed");
			return false;
		}
	}

	/**
	 * @param element
	 * @param msg      - already displayed and not displayed in mentioned, just
	 *                 write the element message
	 * @param timeout- in Seconds
	 * @return true if visible
	 */
	protected boolean isElementDisplayed(MobileElement element, String msg, long timeout) {
		try {
			waitForVisibility(element, timeout);
			custom_Log( msg + " displayed");
			return true;
		} catch (Exception e) {
			custom_Log( msg + " not displayed");
			return false;
		}
	}

	protected boolean isElementDisplayed(WebElement element, String msg, long timeout) {
		try {
			waitForVisibility(element, timeout);
			custom_Log( msg + " displayed");
			return true;
		} catch (Exception e) {
			custom_Log( msg + " not displayed");
			return false;
		}
	}

	protected boolean isElementDisplayed(MobileElement element, long timeout) {
		try {
			waitForVisibility(element, timeout);
			return true;
		} catch (Exception e) {
			return false;
		}
	}


	protected boolean isElementDisplayed(WebElement element, long timeout) {
		try {
			waitForVisibility(element, timeout);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	protected void launchActivity(Activity activityName) 
	{

		((AndroidDriver<MobileElement>) DriverManagers.getDriver()).startActivity(activityName);
	}


	protected void relauchTheApp(){
		closeApp();
		launchApp();
	}

	/**
	 * Launches the app, which was provided in the capabilities at session creation,
	 * and (re)starts the session.
	 */
	protected void launchApp() {
		//custom_Log("App launched with clear data");
		((InteractsWithApps) DriverManagers.getDriver()).launchApp();
	/*	if(getPlatformName().equals(TestUtils.ios())) {

			((InteractsWithApps) DriverManagers.getDriver()).activateApp("com.valyoo.lenskart");

		}else if(getPlatformName().equals(TestUtils.android())) {

			((InteractsWithApps) DriverManagers.getDriver()).activateApp("com.lenskart.app");
			//((InteractsWithApps) DriverManagers.getDriver()).launchApp();

		} */
	}


	/**
	 * Close the app which was provided in the capabilities at session creation and
	 * quits the session.
	 */
	protected void closeApp() {
		//custom_Log( "App closed");		
		((InteractsWithApps) DriverManagers.getDriver()).closeApp();
		/*if(getPlatformName().equals(TestUtils.ios())) {	

			((InteractsWithApps) DriverManagers.getDriver()).terminateApp("com.valyoo.lenskart");

		}else if(getPlatformName().equals(TestUtils.android())) {

			((InteractsWithApps) DriverManagers.getDriver()).terminateApp("com.lenskart.app");
			//((InteractsWithApps) DriverManagers.getDriver()).closeApp();

		}*/
	}


	protected void closeBrowser() {
		custom_Log( "browser closed");		
		DriverManagers.getDriver().close();
	}

	/**
	 * Install an app on the mobile device.
	 * 
	 * @param appPath
	 */
	protected void installApp(String appPath) {
		((InteractsWithApps) DriverManagers.getDriver()).installApp(appPath);
	}

	/**
	 * Remove the specified app from the device (uninstall).
	 * 
	 * @param appName
	 */
	protected void removeApp(String appName) {
		((InteractsWithApps) DriverManagers.getDriver()).removeApp(appName);
	}

	/**
	 * Checks if an app is installed on the device.
	 * 
	 * @param appName
	 * @return True if app is installed, false otherwise.
	 */
	protected boolean isAppInstalled(String appName) {
		return ((InteractsWithApps) DriverManagers.getDriver()).isAppInstalled(appName);
	}

	protected void startApp(String appPackageName) {
		closeApp();
		((InteractsWithApps) DriverManagers.getDriver()).activateApp(appPackageName);
	}


	/**
	 * This method is used to create dynamic xpath
	 * 
	 * @param xpath
	 * @param replacingString
	 * @return
	 */
	protected MobileElement dymanicXpath(String xpath, String replacingString) {
		try {
			String newXpath = String.format(xpath, replacingString);
			waitForVisibility((MobileElement) DriverManagers.getDriver().findElement(By.xpath(newXpath)), 3);
			return (MobileElement) DriverManagers.getDriver().findElement(By.xpath(newXpath));
		} catch (NoSuchElementException | TimeoutException e) {
			return null;
		}
	}




	protected void horizontalScroll(MobileElement element,int time) {


		//waitForVisibility(element,time);
		org.openqa.selenium.Point location = element.getLocation();
		int x = location.getX();
		int y = location.getY();

		System.out.println("X Coordinate: " + x);
		System.out.println("Y Coordinate: " + y);

		TouchAction<?> action = new TouchAction<>(DriverManagers.getDriver());
		action
		.press(PointOption.point(x*20, y))
		.waitAction(WaitOptions.waitOptions(Duration.ofSeconds(1)))
		.moveTo(PointOption.point(x, y))
		.release()
		.perform();
		System.out.println("horizontalScroll sucess");

	}


	//	 MobileElement element = androidDriver.findElement(MobileBy.AndroidUIAutomator(
	//             "new UiScrollable(new UiSelector().scrollable(true)).scrollIntoView(" +
	//                     "new UiSelector().textContains(\"" + elementText + "\"));"));

	//	 MobileElement element = iosDriver.findElement(MobileBy.iOSClassChain(
	//             "**/XCUIElementTypeScrollView[`name == 'scrollView'`]/**/XCUIElementTypeStaticText[`label CONTAINS '" + elementText + "'`]"));

	protected void scrollToButton(MobileElement element,MobileElement scrollElement,String msg){

		boolean eleVisisle=false;
		int scroll=0;

		while(!eleVisisle) {
			try {
				//scroll("up");
				//System.out.print(isElementVisible(scrollElement,"hi halow"));
				waitForVisibility(scrollElement);
				eleVisisle=true;

			}catch(Exception e) {
				scroll("up");
				scroll=scroll+1;
			}
			
			if(scroll>15) {
				Assert.fail();
				break;
			}

		}

		custom_Log( "Scrolled to the element "+msg);
	}

	protected void scrollToButton(MobileElement element,String elementText,String msg){

		try {

			if(getPlatformName().equals(TestUtils.android())) {
				//scroll("up");
				//	scroll("down");
				((FindsByAndroidUIAutomator) DriverManagers.getDriver()).findElementByAndroidUIAutomator(
						"new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains(\""
								+ elementText + "\").instance(0))");
				//	scroll("up");

				custom_Log( "Scrolled to the element: " + elementText);

			}else if(getPlatformName().equals(TestUtils.ios())) {

				/*JavascriptExecutor js = (JavascriptExecutor) DriverManagers.getDriver();
				HashMap scrollObject = new HashMap<>();
				scrollObject.put("predicateString", "value == '" + elementText + "'");
				scrollObject.put("direction", "down");
				js.executeScript("mobile: scroll", scrollObject);

				if(isElementVisible(element, msg)==false) {
					scrollToButton(element, elementText, msg);
				}*/
				//		int countScroll=0;
				//		if(isElementVisible(element, msg)==false&countScroll<=10) {

				scroll("up");scroll("up");

				/*JavascriptExecutor js = (JavascriptExecutor) DriverManagers.getDriver();
				HashMap scrollObject = new HashMap<>();
				scrollObject.put("predicateString", "value == '" + elementText + "'");
				scrollObject.put("direction", "up");
				js.executeScript("mobile: scroll", scrollObject);*/




				int count=0;

				while(isElementVisible(element, msg)==false&count<10) {
					hideKeyboard();
					iosScrollFunction(element, elementText, msg);
					count++;
				}

				if(count>=10) {
					throw new FrameWorkExpection("Unable to scroll to the element");
				}


				custom_Log( "Scrolled to the element: " + elementText);
			}

		} catch (Exception e) {

			System.out.println(e);
			custom_Log( "Unable to scroll to the element " + elementText);
		}



	}


	protected void iosScrollFunction(MobileElement element,String elementText,String msg) {

		JavascriptExecutor js = (JavascriptExecutor) DriverManagers.getDriver();
		HashMap scrollObject = new HashMap<>();
		scrollObject.put("predicateString", "value == '" + elementText + "'");
		scrollObject.put("direction", "down");
		js.executeScript("mobile: scroll", scrollObject);     

	}


	protected void scrollToButton(MobileElement element,String elementText,String upOrDown,String msg){

		//"down"   "up"

		try {

			if(getPlatformName().equals(TestUtils.android())) {

				((FindsByAndroidUIAutomator) DriverManagers.getDriver()).findElementByAndroidUIAutomator(
						"new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains(\""
								+ elementText + "\").instance(0))");

			}else if(getPlatformName().equals(TestUtils.ios())) {

				JavascriptExecutor js = (JavascriptExecutor) DriverManagers.getDriver();
				HashMap scrollObject = new HashMap<>();
				scrollObject.put("predicateString", "value == '" + elementText + "'");
				scrollObject.put("direction", upOrDown);
				js.executeScript("mobile: scroll", scrollObject);

				if(isElementVisible(element, msg)==false) {
					scrollToButton(element, elementText, msg);
				}
			}

		} catch (Exception e) {
			custom_Log( "Unable to scroll to the element " + elementText);
		}

		custom_Log( "Scrolled to the element: " + elementText);

	}


	// Sandeep Neehal
	/*
	 * Scroll to the element visible only text
	 * 
	 * @param String elementText
	 * @param String msg logs + extentReport
	 */
	protected void scrollToElement(String elementText, String msg) {
		try {


			((FindsByAndroidUIAutomator) DriverManagers.getDriver()).findElementByAndroidUIAutomator(
					"new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains(\""
							+ elementText + "\").instance(0))");

			custom_Log( "Scrolled to the element: " + msg);



		} catch (Exception e) {
			custom_Log( "Unable to scroll to the element " + msg);
		}
	}

	/**
	 * Scroll up or down
	 * 
	 * @param upOrDown
	 */
	protected void scroll(String upOrDown) {

		TouchAction action = new TouchAction((PerformsTouchActions) DriverManagers.getDriver());
		PointOption startPoint = new PointOption().withCoordinates(250, 500);
		PointOption endPoint = new PointOption().withCoordinates(150, 150);
		switch (upOrDown.toLowerCase()) {
		case "up":
			startPoint = new PointOption().withCoordinates(250, 500);
			endPoint = new PointOption().withCoordinates(150, 150);
			custom_Log( "Scrolled up");
			break;
		case "down":
			startPoint = new PointOption().withCoordinates(150, 200);
			endPoint = new PointOption().withCoordinates(250, 500);
			custom_Log( "Scrolled down");
			break;
		}
		action.press(startPoint).waitAction(new WaitOptions().withDuration(Duration.ofMillis(500))).moveTo(endPoint)
		.release().perform();
	}

	/**
	 * Swipe touch (UP,DOWN,LEFT,RIGHT)
	 *
	 * @param direction direction
	 * @param count     count
	 */
	protected void swipe(String direction, int count, int time) {
		Dimension size = DriverManagers.getDriver().manage().window().getSize();
		try {
			switch (direction) {
			case "left":
			case "LEFT":
				for (int i = 0; i < count; i++) {
					int startx = (int) (size.width * 0.8);
					int endx = (int) (size.width * 0.20);
					int starty = size.height / 2;
					touchActions(startx, starty, endx, starty, time);
				}
				break;
			case "right":
			case "RIGHT":
				for (int j = 0; j < count; j++) {
					int endx = (int) (size.width * 0.8);
					int startx = (int) (size.width * 0.20);
					int starty = size.height / 2;
					touchActions(startx, starty, endx, starty, time);
				}
				break;
			case "up":
			case "UP":
				for (int j = 0; j < count; j++) {
					int starty = (int) (size.height * 0.80);
					int endy = (int) (size.height * 0.20);
					int startx = size.width / 2;
					touchActions(startx, starty, startx, endy, time);
				}
				break;
			case "down":
			case "DOWN":
				for (int j = 0; j < count; j++) {
					int starty = (int) (size.height * 0.80);
					int endy = (int) (size.height * 0.20);
					int startx = size.width / 2;
					touchActions(startx, endy, startx, starty, time);
				}
				break;
			default:
				custom_Log("Direction not found");
				break;
			}
		} catch (Exception e) {
			custom_Log("Exception caught while performing Swipe " + e);
		}
	}

	/*
	 * Swipe with axis
	 *
	 * @param x    x axis
	 * @param y    y axis
	 * @param x1   x1 axis
	 * @param y1   y1 axis
	 * @param time timeInMilli
	 */
	@SuppressWarnings("rawtypes")
	protected void touchActions(int a1, int b1, int a2, int b2, int time) {
		TouchAction touchAction = new TouchAction((PerformsTouchActions) DriverManagers.getDriver());
		touchAction.press(PointOption.point(a1, b1)).waitAction(WaitOptions.waitOptions(Duration.ofMillis(time)))
		.moveTo(PointOption.point(a2, b2)).release();
		touchAction.perform();
	}

	/**
	 * Hide keyboard mehod if visible
	 */
	protected void hideKeyboard_Android() {
		if (((AndroidDriver) DriverManagers.getDriver()).isKeyboardShown()) {
			try {
				custom_Log( "Hide keyboard");
				((AndroidDriver) DriverManagers.getDriver()).hideKeyboard();
			} catch (Exception e) {
				custom_Log("Keyboard already hidden");
			}
		}
	}

	/**
	 * Navigate back from phone back key
	 */
	protected void navigateBack() {
		try {
			custom_Log( "Navigating back to previous page");
			
			((PressesKey) DriverManagers.getDriver()).pressKey(new KeyEvent().withKey(AndroidKey.BACK));
		} catch (Exception e) {
			custom_Log("Error occured while navigating back -> " + e);
		}
	}

	/***
	 * Press long gesture on the desire element
	 */
	protected  void longPress(MobileElement element) {
		try {
			TouchActions action = new TouchActions(DriverManagers.getDriver());
			action.longPress(element);
			action.perform();
			custom_Log( "Long pressed on element successfully");

		} catch (Exception e) {
			custom_Log("Exception Occured while long press actions " + e);
		}
	}

	// Method to tap on the unresponsive element
	/**
	 * @param element
	 * @param leftEdge  in percentage
	 * @param rightEdge in percentage
	 */
	protected void tapElement(MobileElement element, int leftEdge, int rightEdge) {
		try {
			if (leftEdge <= 100 && rightEdge <= 100) {
				double left = (double) leftEdge / 100;
				double right = (double) rightEdge / 100;
				tapElementAt(element, left, right);
				custom_Log("Tapped on Element at " + left + " from left and " + right + " from right.");
			} else {
				custom_Log("the left and right co-ordinate should be less than or equal to 100");
			}
		} catch (Exception e) {
			custom_Log("Incorrect percentage " + e.toString());
		}
	}

	protected void tapAtPoint(Point point) {
		AppiumDriver<MobileElement> d = (AppiumDriver<MobileElement>) DriverManagers.getDriver();
		PointerInput input = new PointerInput(Kind.TOUCH, "finger1");
		Sequence tap = new Sequence(input, 0);
		tap.addAction(input.createPointerMove(Duration.ZERO, Origin.viewport(), point.x, point.y));
		tap.addAction(input.createPointerDown(MouseButton.LEFT.asArg()));
		tap.addAction(new Pause(input, Duration.ofMillis(200)));
		tap.addAction(input.createPointerUp(MouseButton.LEFT.asArg()));
		d.perform(ImmutableList.of(tap));
	}

	protected void tapElementAt(MobileElement el, double xPct, double yPct) {
		Rectangle elRect = el.getRect();
		Point point = new Point(elRect.x + (int) (elRect.getWidth() * xPct),
				elRect.y + (int) (elRect.getHeight() * yPct));
		tapAtPoint(point);
	}

	/*
	 * This method is using to open app using deeplink url's
	 * 
	 * @param String URL
	 */
	protected void openAppWithDeeplink(String url) {

		if(getPlatformName().equals(TestUtils.android())) {
			HashMap<String, String> deeplinkUrl = new HashMap<String, String>();
			deeplinkUrl.put("url", url);
			deeplinkUrl.put("package", "com.lenskart.app");


			((RemoteWebDriver) DriverManagers.getDriver()).executeScript("mobile:deepLink", deeplinkUrl);

			//((RemoteWebDriver) DriverManagers.getDriver()).executeScript("mobile:deepLink",  url);

			custom_Log("Opening page using deeplink " + url);

		}else if(getPlatformName().equals(TestUtils.ios())) {


			/*	Map<String, Object> args = new HashMap<>();
			args.put("bundleId", "com.valyoo.lenskart");
			args.put("shouldWaitForQuiescence", true);
			args.put("arguments", Arrays.asList("-u", url));
			DriverManagers.getDriver().executeScript("mobile:activateApp", args);*/

			/*Map<String, Object> args = ImmutableMap.of(
					"bundleId", "com.valyoo.lenskart",
					"arguments", ImmutableList.of("-u", url)
					);

			DriverManagers.getDriver().executeScript("mobile:deepLink", args);

			custom_Log("Opening page using deeplink " + url);*/
			
			
			
			
			DriverManagers.getDriver().runAppInBackground(Duration.ofSeconds(-1));
		    
		    
			DriverManagers.getDriver().get(url);
		}

	}

	/**
	 * This method open deeplink in app and return the generic page object
	 * 
	 * @param <T>
	 * @param url
	 * @param T
	 * @return
	 */
	protected <T> T openAppWithDeeplink(String url, T T) {
		HashMap<String, String> deeplinkUrl = new HashMap<String, String>();
		deeplinkUrl.put("url", url);
		deeplinkUrl.put("package", "com.lenskart.app");
		((RemoteWebDriver) DriverManagers.getDriver()).executeScript("mobile:deepLink", deeplinkUrl);
		custom_Log("Opening page using deeplink " + url);
		return T;

	}

	protected void pressEnter() {
		if (((AndroidDriver) DriverManagers.getDriver()).isKeyboardShown()) {
			//((AndroidDriver) DriverManagers.getDriver()).pressKey(new KeyEvent(AndroidKey.ENTER));
			
			((AndroidDriver) DriverManagers.getDriver()).executeScript("mobile: performEditorAction", 
			        ImmutableMap.of("action", "search"));
			
			
			custom_Log("Clicked on Mobile ENTER key");
		} else {
			custom_Log("keyboard did not visible on UI");
		}
	}


	protected void sendKeysEnterFunction(MobileElement e) {

		if(getPlatformName().equals(TestUtils.android())) {
			pressEnter();
			//e.sendKeys(Keys.RETURN);
			custom_Log( "Clicked on Mobile ENTER key");
		}else if(getPlatformName().equals(TestUtils.ios())) {
			e.sendKeys(Keys.RETURN);
			custom_Log( "Clicked on Mobile ENTER key");
		}

	}



	/*
	 * Return boolean if element is displayed with log and Extent report
	 * 
	 * @param MobileElement
	 * @param msg           logs + Extentreport
	 * @return true if element is displayed
	 */
	protected boolean isElementSelected(MobileElement element, String msg) {
		try {
			waitForVisibility(element, 4);
			if (element.isSelected()) {
				custom_Log( msg + " selected");
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}

	protected boolean isElementSelected(WebElement element, String msg) {
		try {
			waitForVisibility(element, 4);
			if (element.isSelected()) {
				custom_Log( msg + " selected");
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}

	/*
	 * Return boolean if element is displayed with log and Extent report
	 * 
	 * @param MobileElement
	 * @param msg           logs + Extentreport
	 * @return true if element is displayed
	 */
	protected boolean isElementDeSelected(MobileElement element, String msg) {

		try {
			if (element.isSelected() == false) {
				custom_Log( msg + " deselected");
				return true;
			} else {
				custom_Log( msg + " deselected");
				return true;

			}

		} catch (Exception e) {
			return true;
		}

	}

	protected boolean isElementDeSelected(WebElement element, String msg) {

		try {
			if (element.isSelected() == false) {
				custom_Log( msg + " deselected");
				return true;
			} else {
				custom_Log( msg + " deselected");
				return true;

			}

		} catch (Exception e) {
			return true;
		}

	}

	/**
	 * This method upload image into current device from:
	 * /LKAppTDDFramework/image.png to: /sdcard/Download/SampleMedia.png
	 */
	protected void uploadimageIntoDevice() {
		try {
			((PushesFiles) DriverManagers.getDriver()).pushFile("/sdcard/Download/SampleMedia.png",
					new File("image.png"));
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}
	}

	/**
	 * This method splits the string value in two parts and returns the end value
	 */
	protected String splitString(String value) {
		String[] splittedValue = value.split("\\s+");
		return (splittedValue[splittedValue.length - 1]);
	}

	/**
	 * This method compares two numeric string
	 * 
	 * @param msg logs + Extentreport
	 */
	protected void compareTwoNumericStrings(String value1, String value2, String msg) {
		if (Integer.parseInt(value1) == Integer.parseInt(value2)) {
			custom_Log( msg + " same");
		} else if (Integer.parseInt(value1) < Integer.parseInt(value2)) {
			custom_Log( msg + " more");
		} else {
			custom_Log( msg + " less");
		}
	}

	protected void closeAndLaunchApp() {
		closeApp();
		launchApp();
		custom_Log("close and lauch the app");
	}

	protected void customeLogs(String msg) {
		custom_Log(msg);
	}


	protected void  backButton(){
		DriverManagers.getDriver().navigate().back();
		custom_Log("navigate to back");

	}

	protected void hideKeyboard(){
		DriverManagers.getDriver().hideKeyboard();
		custom_Log("hide the key board");
	}

	protected void allowNotifications() {
		DriverManagers.getDriver().switchTo().alert().accept(); 
		custom_Log("popup allow acceess");
	}
	/*
	protected Object getWhatsAppNumber() {
		JavascriptExecutor jse = (JavascriptExecutor)DriverManagers.getDriver();
		return jse.executeScript("browserstack_executor:{\"action\": \"deviceInfo\", \"arguments\" : {\"deviceProperties\" : ['simOptions']}}");
	}
	 */





	protected boolean isElementEnabled(WebElement element){
		waitForVisibility(element);
		try {
			return element.isEnabled();
		}catch (Exception e){
			return false;
		}
	}




	protected boolean selectDropDownByValueIfEnabled(WebElement webElement, String value){
		if(isElementEnabled(webElement)){
			selectDropDownByValue(webElement,value,"Selected "+value+" from the dropdown");
			return true;
		}return false;
	}

	protected void selectDropDownByValue(WebElement element , String value,String message){
		waitForVisibility(element);
		Select select = new Select(element);
		select.selectByValue(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void selectDropDownByValue(By by , String value,String message){
		waitForVisibility(by);
		Select select = new Select(DriverManagers.getDriver().findElement(by));
		select.selectByValue(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void selectDropDownByText(WebElement element , String value,String message) {
		waitForVisibility(element);
		Select select = new Select(element);
		select.selectByVisibleText(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void selectDropDownByText(WebElement element , String value,String message,long timeout) {
		waitForVisibility(element,timeout);
		Select select = new Select(element);
		select.selectByVisibleText(value);
		custom_Log(message+" "+value+" from the dropdown");
	}


	protected void selectDropDownByIndex(WebElement element , int value,String message){
		waitForVisibility(element);
		Select select = new Select(element);
		select.selectByIndex(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void selectDropDownByIndex(WebElement element , int value,String message,long timeout){
		waitForVisibility(element,timeout);
		Select select = new Select(element);
		select.selectByIndex(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void deSelectDropDownByIndex(WebElement element , int value,String message){
		waitForVisibility(element);
		Select select = new Select(element);
		select.deselectByIndex(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void deSelectDropDownByValue(WebElement element , String value,String message){
		waitForVisibility(element);
		Select select = new Select(element);
		select.deselectByValue(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected void deSelectDropDownByText(WebElement element , String value,String message){
		waitForVisibility(element);
		Select select = new Select(element);
		select.deselectByVisibleText(value);
		custom_Log(message+" "+value+" from the dropdown");
	}

	protected List<WebElement> getListOfAllElementsInDropDown(WebElement element){
		waitForVisibility(element);
		Select select = new Select(element);
		return select.getOptions();
	}

	protected List<WebElement> getListOfAllElementsInDropDown( By by){
		waitForVisibility(by);
		Select select = new Select(DriverManagers.getDriver().findElement(by));
		return select.getOptions();
	}

	protected String getTextOfSelectedElement(WebElement element, String msg){
		waitForVisibility(element);
		Select select = new Select(element);
		String s;
		try {
			s = getTextOfElement(select.getFirstSelectedOption(),msg);
			if(s.length() == 0) s = null;
		}catch (Exception e) {
			custom_Log("No option selected for the given dropdown");
			s= null;
		}
		return s;
	}

	protected String getTextOfSelectedElement(By by,@Nullable String msg){
		waitForVisibility(by);
		Select select = new Select(DriverManagers.getDriver().findElement(by));
		String s;
		try {
			s = getTextOfElement(select.getFirstSelectedOption());
			if(s.trim().length() == 0) s = null;
			custom_Log(msg +": "+(Objects.isNull(s)?"No option selected":s));
		}catch (Exception e) {
			custom_Log("No option selected for the given dropdown");
			s= null;
		}
		return s;
	}


	protected void moveMouseToElement(WebElement webElement,String message){
		waitForVisibility(webElement);
		Actions actions = new Actions(DriverManagers.getDriver());
		actions.moveToElement(webElement).build().perform();
		custom_Log(message);
	}

	protected void refreshThePage() {
		DriverManagers.getDriver().navigate().refresh();
		custom_Log( "refrshed page");
	}

}
