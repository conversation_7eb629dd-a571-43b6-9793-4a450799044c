package com.qa.driverManagers;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import org.apache.logging.log4j.ThreadContext;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;

import com.qa.utils.TestUtils;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.remote.MobileCapabilityType;
import io.appium.java_client.service.local.AppiumDriverLocalService;
import lombok.Getter;

public class DriverManagers {
	
	
	protected static ThreadLocal<AppiumDriver> driver=new ThreadLocal<AppiumDriver>();
	
	
	
	protected static ThreadLocal<TestUtils> utils=new  ThreadLocal<TestUtils>();
	

	
	
	protected static ThreadLocal<Properties> props=new ThreadLocal<Properties>();
	


	
	protected static ThreadLocal<HashMap<String, String>> strings=new ThreadLocal<HashMap<String, String>>();

	
	protected static ThreadLocal<String> deviceName=new ThreadLocal<String>();
	
	
	protected static ThreadLocal<String> platformName=new ThreadLocal<String>();
	
	protected static ThreadLocal<String> platformVersoin=new ThreadLocal<String>();
	
	
	protected static ThreadLocal<String> dateAndTime=new ThreadLocal<String>();
	
	protected static ThreadLocal<String> automationRunningCountryName=new ThreadLocal<String>();
	
	protected static ThreadLocal<String> loginUser=new ThreadLocal<String>();
	
	
	public static String getCountry() {
		return automationRunningCountryName.get();
	}
	public static void setCountry(String country) {
		automationRunningCountryName.set(country);
	}
	
	
	public static String getloginUser() {
		return loginUser.get();
	}
	public static void setloginUser(String loginUser1) {
		loginUser.set(loginUser1);
	}

	
	public static String getDateAndTime() {
		return dateAndTime.get();
	}
	public static void setDateAndTime(String dateAndTime1) {
		dateAndTime.set(dateAndTime1);
	}

	public static String getDeviceName() {
		return deviceName.get();
	}
	public static void setDeviceName(String deviceName1) {
		deviceName.set(deviceName1);
	}

	public static String getPlatformName() {
		return platformName.get();
	}
	public static void setPlatformName(String platformVersion) {
		platformName.set(platformVersion);
	}
	
	public static String getPlatformVersion() {
		return platformVersoin.get();
	}
	public static void setPlatformVersion(String platformVersion) {
		platformVersoin.set(platformVersion);
	}

	public static  AppiumDriver getDriver() {
		return driver.get();
	}
	public static void setDiver(AppiumDriver driver2) {
		driver.set(driver2);
	}

	public static Properties getProps() {
		return props.get();
	}
	public static void setProps(Properties props2) {
		props.set(props2);
	}

	public static TestUtils getUtils() {
		return utils.get();
	}
	public static void setUtils(TestUtils utils2) {
		utils.set(utils2);
	}

	public static HashMap<String, String> getStrings() {
		return strings.get();
	}
	public static void setStrings(HashMap<String, String> strings2) {
		strings.set(strings2);
	}

	public synchronized  AppiumDriver startAppDriver(String automationRunningPlatform,String platformName,String udid,String deviceName,String platformVersion,String country,String browserName,String loginUser) throws IOException{

		InputStream inputStream=null;
		InputStream stringsis=null;
		Properties props=new Properties();
		TestUtils utils=new TestUtils();
		AppiumDriver driver = null;

		String dateAndTime=utils.getDateTime();
		
		//this.platformName=platformName;
		setPlatformName(platformName);
		
		setPlatformVersion(platformVersion);
		
		setDeviceName(deviceName);
		
		setDateAndTime(dateAndTime);
		
		setUtils(utils);
		
		setloginUser(loginUser);

		//LOGS FOR PARALLEL
		String strFile="logs"+File.separator+platformName+File.separator+deviceName;
		File LogFile=new File(strFile);
		if(!LogFile.exists()){
			LogFile.mkdirs();
		}
		ThreadContext.put("ROUTINGKEY",strFile);

		try {

			//propertifile
			//props=new Properties();
			setProps(props);
			String propFileName="config.properties";

			inputStream=getClass().getClassLoader().getResourceAsStream(propFileName);
			props.load(inputStream);


			//string property file
			String xmlFileName="strings/IN/strings.xml";
			stringsis=getClass().getClassLoader().getResourceAsStream(xmlFileName);


			setStrings(utils.parseStringXML(stringsis));
			setCountry(country);
			
			switch(automationRunningPlatform) {
			  case "android":
				  driver=new SetAndroidDriver().setAndroidDriver(platformName, deviceName, udid, props);
			    break;
			 
			  case "browserStackAndroid":
				  driver=new BrowserStackAndroidDriver().setAndroidDriver(deviceName, platformVersion, country, props);
				  break;
			 
				  
			}
			setDiver(driver);
			driver.manage().timeouts().implicitlyWait(5, TimeUnit.SECONDS);

		}catch(Exception e){
			e.printStackTrace();



		}finally {

			if(inputStream !=null) {
				inputStream.close();
			}

			if(stringsis !=null) {
				stringsis.close();
			}
		}
		return getDriver();
	}

}