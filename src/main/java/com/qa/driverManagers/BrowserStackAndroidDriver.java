package com.qa.driverManagers;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import org.openqa.selenium.remote.DesiredCapabilities;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.MobileCapabilityType;

public class BrowserStackAndroidDriver {



	public AndroidDriver setAndroidDriver(String deviceName,String platformVersion,String country,Properties props) throws MalformedURLException{

		//String userName="ragul_HiT6eA";
		//String automateKey="********************";

		String browserStackURL="https://"+props.getProperty("browserUserName")+":"+props.getProperty("browserAutomateKey")+"@hub-cloud.browserstack.com/wd/hub";
		
		
		
		DesiredCapabilities caps=new DesiredCapabilities();
		
		

		caps.setCapability(MobileCapabilityType.DEVICE_NAME, deviceName);
		caps.setCapability(MobileCapabilityType.PLATFORM_VERSION, platformVersion);
		caps.setCapability("interactiveDebugging", "true");

		//caps.setCapability("browserstack.gpsLocation", props.getProperty(country));
		
		caps.setCapability("browserstack.geoLocation", "IN");
		caps.setCapability("browserstack.networkLogs", "true");
		caps.setCapability("browserstack.acceptInsecureCerts", "true");
		caps.setCapability("networkLogs", "true");
		//caps.setCapability("build", "5.1");
		//caps.setCapability("naem", "BrowseerStack Sample Test");

		caps.setCapability(MobileCapabilityType.APP, props.getProperty("androidApp"));
		
		//otp
		/*caps.setCapability("browserstack.enableSim","true");
		HashMap<String, String> simOptions = new HashMap<String, String>();
		simOptions.put("region","USA");
		caps.setCapability("browserstack.simOptions", simOptions);*/

		//URL url=new URL(props.getProperty("appiumUrl"));

		URL url=new URL(browserStackURL);

		return new AndroidDriver(url,caps);
	}



}
