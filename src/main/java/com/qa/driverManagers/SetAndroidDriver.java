package com.qa.driverManagers;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import org.openqa.selenium.remote.DesiredCapabilities;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.MobileCapabilityType;

public class SetAndroidDriver {
	
	
	public AndroidDriver setAndroidDriver(String platformName,String deviceName,String udid,Properties props) throws MalformedURLException{
		
		

		DesiredCapabilities caps=new DesiredCapabilities();

		caps.setCapability(MobileCapabilityType.PLATFORM_NAME, platformName);
		caps.setCapability(MobileCapabilityType.DEVICE_NAME, deviceName);
		caps.setCapability(MobileCapabilityType.AUTOMATION_NAME, props.getProperty("androidAutoamtioinName"));
		//caps.setCapability("avd", deviceName);
		caps.setCapability(MobileCapabilityType.UDID, udid);
		//caps.setCapability("avdLaunchTimeout", props.getProperty("simulatorAndAvdTimeOut"));
		caps.setCapability("appPackage", props.getProperty("androidAppPackage"));
		caps.setCapability("appActivity", props.getProperty("androidAppActivity"));

		URL url=new URL(props.getProperty("appiumUrl"));
		
		return new AndroidDriver(url,caps);
		
	}

}
