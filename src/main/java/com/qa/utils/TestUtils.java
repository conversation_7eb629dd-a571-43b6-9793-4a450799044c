package com.qa.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Random;
import java.util.function.BinaryOperator;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.qa.driverManagers.DriverManagers;

public class TestUtils {

	public static final long wait=30;


	public static HashMap<String, String> parseStringXML(InputStream file) throws Exception {

		HashMap<String, String> stringMap = new HashMap<String, String>();

		// Get DOM Builder
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		DocumentBuilder builder = factory.newDocumentBuilder();

		// Build Document
		Document document = builder.parse(file);

		// Normalize the XML Structure;
		document.getDocumentElement().normalize();

		// root node
		Element root = document.getDocumentElement();
		// System.out.println(root.getNodeName());

		// Get all elements
		NodeList nList = document.getElementsByTagName("string");

		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node node = nList.item(temp);
			if (node.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) node;
				stringMap.put(eElement.getAttribute("name"), eElement.getTextContent());
			}
		}
		return stringMap;
	}


	public static String getDateTime() {
		SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
		Date date = new Date();
		return formatter.format(date);
	}

	public static String getDateTime(String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		Date date = new Date();
		return formatter.format(date);
	}

	public static String getDate() {
		SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");
		Date date = new Date();
		return formatter.format(date);
	}
	
	public static String getTime() {
		SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
		Date date = new Date();
		return formatter.format(date);
	}


	public String dateTime() {

		DateFormat dateFormate=new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
		Date date=new Date();
		return dateFormate.format(date);
	}


	public void log(String txt) {


		String msg=Thread.currentThread().getId()+":"+DriverManagers.getPlatformName()+":"+DriverManagers.getDeviceName()+":"
				+Thread.currentThread().getStackTrace()[2].getClassName()+":"+txt;



		System.out.println(msg);

		String strFile="logs"+File.separator+DriverManagers.getPlatformName()+File.separator+DriverManagers.getDeviceName()+File.separator+DriverManagers.getDateAndTime();

		File logFile=new File(strFile);

		if(!logFile.exists()) {
			logFile.mkdirs();
		}

		FileWriter fileWriter=null;
		try {

			fileWriter=new FileWriter(logFile+File.separator+"log.txt",true);

		}catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}

		PrintWriter prinWriter=new PrintWriter(fileWriter);
		prinWriter.println(msg);
		prinWriter.close();

	}

	public Logger log() {
		/*static Logger logs=LogManager.getLogger(BaseTest.class.getName());
		logs.info("this is info message");
		logs.error("this is info error");
		logs.debug("this is info debug");
		logs.warn("this is info warn");*/


		return LogManager.getLogger(Thread.currentThread().getStackTrace()[2].getClassName() );
	}


/*	public static int randomNumberGen(int min, int max) {
		BinaryOperator<Integer> bi = (a, b) -> new Random().nextInt(b - a) + a;
		return bi.apply(min, max);
	}*/



	public JSONObject getJsonData(InputStream datais, String dataFile) {
		try {
			datais = getClass().getClassLoader().getResourceAsStream(dataFile);
			JSONTokener tokener = new JSONTokener(datais);
			return new JSONObject(tokener);
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			if (datais != null) {
				try {
					datais.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	public static String android() {

		return "android";
	}
	public static String ios() {

		return "ios";
	}
	
	
	public static int ramdomBetweenInTheNumber(int value) {	

		int arr[] = new int[value];

		for(int i=0;i<value;i++) {
			arr[i]=i;			
		}		
		double ran = Math.random();
		String ranNUB=ran+"";
		String num = String.valueOf(ranNUB.charAt(ranNUB.length()-2));
		int number = Integer.parseInt(num);

		if(number<value&&number!=0) return arr[number];
		else return 1;

	}
	
	
	public static String msiteOrderID(String orderText) {
		String orderid;
		int n = orderText.indexOf('#');


		if(orderText.contains("#")) {
			orderid= orderText.substring(1, orderText.length());
		}else {
			orderid=orderText;
		}


		return orderid;
	}
	
	
	public static synchronized HashMap<String, String> parseStringXML(String path) {
		InputStream file = null;
		try {
			file = new FileInputStream(path);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		HashMap<String, String> stringMap = new HashMap<>();
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		Document document = null;
		try {
			DocumentBuilder builder = factory.newDocumentBuilder();
			document = builder.parse(file);
		}catch (Exception e){
			e.printStackTrace();
		}
		assert document != null;
		document.getDocumentElement().normalize();
		Element root = document.getDocumentElement();
		NodeList nList = document.getElementsByTagName("string");

		for (int temp = 0; temp < nList.getLength(); temp++) {
			Node node = nList.item(temp);
			if (node.getNodeType() == Node.ELEMENT_NODE) {
				Element eElement = (Element) node;
				stringMap.put(eElement.getAttribute("name"), eElement.getTextContent());
			}
		}
		return stringMap;
	}
	
	
	public static boolean searchTextIsNumber(String searchText) {

		
		
		try {
		int h=Integer.parseInt(searchText); 
		return true;
		
		}catch (NumberFormatException e) {
			return false;
		}				
	
	}
	
	public static String changenumberToText(int number) {
		
		return number+"";
		
	}
	

	
	
	/*public static void orderIdLogs(String orderId) {
		writeInFile(orderId, "Orders");
		FrameworkLogger.logs(LogType.EXTCONWRT, "Order id: " + orderId);
	}



	public static FileWriter writer;

	public static void writeInFile(String data, String fileName) {
		try {
			writer = new FileWriter(fileName + ".txt", true);
			writer.write("\r\n");
			writer.write(data);
			writer.close();
//log().info("writen sucessfully in logger file");
		} catch (Exception e) {
			log().error("Exception occur while writing in file,Exception " + e);
		}
	}*/

}
