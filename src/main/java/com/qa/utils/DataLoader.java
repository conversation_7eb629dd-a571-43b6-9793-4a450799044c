package com.qa.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qa.driverManagers.DriverManagers;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public final class DataLoader {

	private static HashMap<String, String> appStrings;



	private DataLoader() {}

	public static synchronized String getDesktopString(String string){
		if(appStrings == null) appStrings = TestUtils.parseStringXML("src/test/resources/strings/IN/strings.xml");
		return appStrings.get(string);
	}


	private static HashMap<String,HashMap<String,String>> addressData;

	
	

//	<!-- SA, SG, AE,IN, US -->
	public static synchronized HashMap<String,String> getAddressData(String key){
		if(addressData == null) {
			try {
				if(DriverManagers.getCountry().equals("IN")) {

					addressData =  new ObjectMapper().readValue(new File("src/test/resources/data/IN/addressData.json"), new TypeReference<HashMap<String,HashMap<String,String>>>() {});
				}

			} catch (IOException ignored) {}
		}
		return addressData.get(key);
	}



}
