package com.qa.appPages;

import static org.testng.Assert.assertTrue;

import java.util.HashMap;

import org.json.JSONObject;

import com.qa.BaseTest;
import com.qa.expection.FrameWorkExpection;
import com.qa.utils.Wait;

import io.appium.java_client.MobileElement;
import io.appium.java_client.pagefactory.AndroidFindBy;
import io.appium.java_client.pagefactory.iOSXCUITFindBy;

public class AppAddressPage extends BaseTest{

/*
	@AndroidFindBy(xpath  = "//android.widget.TextView[@resource-id='com.lenskart.app.store:id/tv_toolbar_title']|//android.widget.TextView[@resource-id='com.lenskart.app:id/tv_title']")
	@iOSXCUITFindBy(xpath = "//XCUIElementTypeStaticText[@name='Change Address']|//XCUIElementTypeStaticText[@name='Select address']|//XCUIElementTypeStaticText[@name='Add address manually']|//XCUIElementTypeStaticText[@name='My addresses']")
	private MobileElement addressPagePopUp;
	
	
	
	public boolean savedAddressOptionIsVisible() {
		return isElementDisplayed(addressOptions,"saved address option is",10);
	}




	public AppPaymentPage clickOnAddressFirstOption() {
		clickEvent(addressFirstOptionButton, "Clicked on address First Option");
		return new AppPaymentPage();
	}

	

	public AppPaymentPage selectAddressOrAddNewAddress(HashMap<String, String> addressData) {

		naviagetToAddressPageFromShippingPage();

		assertTrue(addressPagePopUpOrAddressPageIsVisible(), "Unable to load address page");
		//Wait.waitFor(30);

		if(isElementDisplayed(addressPage,"address page is",10)) {
			
			return clickOnAddressFirstOption();
			 
		}else if(isElementDisplayed(addressPopUp,"saved address cart popup is",10)){
			
			 clickOnAddressFirstOption();
			 return new AppCartPage().changePaymentTypeOrProceedToPaymentPage();

		}else if(addNewaddressPageIsVisible()){

			return addNewAddressMap(addressData);

		}else {
			throw new FrameWorkExpection("address page not visible");
		}


	}
	*/
	

}
