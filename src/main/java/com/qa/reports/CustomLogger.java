package com.qa.reports;

import java.io.FileWriter;

import com.qa.BaseTest;
import com.qa.driverManagers.DriverManagers;
import com.qa.expection.FrameWorkExpection;
import com.qa.utils.TestUtils;

public class CustomLogger {
	
	
	public static FileWriter writer;
	
	//final static String bannerResultfilePath="homePageBannerResult/homePageBannerResult.csv";
	
	
	public static void writeInFile(String data, String fileName) {
		try {
			writer = new FileWriter(fileName + ".txt", true);
			writer.write("\r\n");
			writer.write(data);
			writer.close();
		} catch (Exception e) {
			throw new FrameWorkExpection("Exception occur while writing in file,Exception " + e);
		}
	}
	
	public static void searchFailDataFile(String searchText,String passOrFail, String fileName) {
		try {
			writer = new FileWriter("seachOutput/"+fileName + ".csv", true);
			writer.write("\r\n");
			writer.write(searchText);
			writer.write(",");
			writer.write(passOrFail);
			writer.close();
		} catch (Exception e) {
			throw new FrameWorkExpection("Exception occur while writing in file,Exception " + e);
		}
	}
	
	
	public static void printDeviceNameAndVersion(String deviceName, String deviceVersion) {
		try {
			writer = new FileWriter("homePageBannerResult.csv", true);
			writer.write("\r\n");
			writer.write("deviceName: "+deviceName+" versioin"+deviceVersion);
			writer.close();
		} catch (Exception e) {
			throw new FrameWorkExpection("Exception occur while writing in file,Exception " + e);
		}
	}
	
	public static void orderIdLogs(String orderId) {
		writeInFile(orderId, "Orders");
		
	}
	
	public static void searchFailData(String searchData,String passOrFail) {
		
		
		searchFailDataFile(searchData,passOrFail, BaseTest.getPlatformName()+"searchData");
		
	}
	
	public static void homePageBannerRestul(String cacatagory,String catagoryPosision,String bannerName,String bottomBannerposision,String plpPageIsVisilbe) {
		try {
			writer = new FileWriter("homePageBannerResult.csv", true);
			writer.write("\r\n");
			writer.write(cacatagory);
			writer.write(",");
			writer.write(catagoryPosision);
			writer.write(",");
			writer.write(bannerName);
			writer.write(",");
			writer.write(bottomBannerposision);
			writer.write(",");
			writer.write(plpPageIsVisilbe);
			writer.close();
//log().info("writen sucessfully in logger file");
		} catch (Exception e) {
			throw new FrameWorkExpection("Exception occur while writing in file,Exception " + e);
		}
	}
	
	
	
	
}
