package com.qa.reports;

import java.util.HashMap;
import java.util.Map;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
import com.aventstack.extentreports.reporter.configuration.Theme;
import com.qa.driverManagers.DriverManagers;
import com.qa.utils.TestUtils;

public class ExtentReport {
	
	
	static ExtentReports extent;
	//final static String filePath="ExstendReport/"+DriverManagers.getPlatformName()+"/Extent "+DriverManagers.getPlatformName()+" Report"+TestUtils.getDateTime()+".html";
	final static String filePath="ExtendReport/"+DriverManagers.getPlatformName()+"_Report.html";
	
	static Map<Integer,ExtentTest> extentTestMap=new HashMap();
	
	public synchronized static ExtentReports getReporter() {
		if(extent==null) {
			
		ExtentHtmlReporter html=new ExtentHtmlReporter(filePath);
		html.config().setDocumentTitle("Lensakrt App");
		html.config().setReportName("SF_QE");
		html.config().setTheme(Theme.DARK);
			extent=new ExtentReports();
			extent.attachReporter(html);
		}
		
		return extent;
	}
	
	
	public static synchronized ExtentTest getTest() {
		return (ExtentTest) extentTestMap.get((int)(long)(Thread.currentThread().getId()));
	}
	
		
	public static synchronized ExtentTest startTest(String testName, String desc) {

		ExtentTest test = getReporter().createTest(testName, desc);
		extentTestMap.put((int) (long) (Thread.currentThread().getId()), test);
		return test;
	}
	
	

}
