#log4j
status = error
dest = err
name = PropertiesConfig

#console appender 
appender.console.type = Console
appender.console.name = STDOUT
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} %c:%L - %m%n

#Rolling appender
appender.routing.type = Routing
appender.routing.name = MyRoutingAppender
appender.routing.routes.type = Routes
appender.routing.routes.pattern = $${ctx:ROUTINGKEY}
appender.routing.routes.route.type = Route


appender.routing.routes.route.rolling.type = RollingFile
appender.routing.routes.route.rolling.name = ROLLINGFILE
appender.routing.routes.route.rolling.fileName = ${ctx:ROUTINGKEY}/application.log
appender.routing.routes.route.rolling.filePattern = ${ctx:ROUTINGKEY}/$${date:yyyy-MM-dd}/application-%d{yyyy-MM-dd}-%i.log
appender.routing.routes.route.rolling.layout.type = PatternLayout
appender.routing.routes.route.rolling.layout.pattern = [${ctx:ROUTINGKEY} %-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} %c:%L - %m%n
appender.routing.routes.route.rolling.policies.type = Policies
appender.routing.routes.route.rolling.policies.time.type = TimeBasedTriggeringPolicy
#appender.rolling.policies.time.interval = 2
#appender.rolling.policies.time.modulate = true
appender.routing.routes.route.rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.routing.routes.route.rolling.policies.size.size=100MB
appender.routing.routes.route.rolling.strategy.type = DefaultRolloverStrategy
appender.routing.routes.route.rolling.strategy.max = 10

#logger
logger.app.name = com.qa
logger.app.level = debug
logger.app.additivity = false
logger.app.appenderRef.console.ref = STDOUT
logger.app.appenderRef.file.ref = MyRoutingAppender

 
#rootLogger
rootLogger.level = info
rootLogger.appenderRef.stdout.ref = STDOUT

#file appender
#appender.file.type = File
#appender.file.name = FILE
#appender.file.fileName = logs/application.log
#appender.file.layout.type = PatternLayout
#appender.file.layout.pattern = [%-5level] %d{yyyy-MM-dd HH:mm:ss.SSS} %c:%L - %m%n
#appender.file.append = false

#logger
#logger.app.name = com.qa
#logger.app.level = debug
#logger.app.additivity = false
#logger.app.appenderRef.console.ref = STDOUT
#logger.app.appenderRef.file.ref = FILE