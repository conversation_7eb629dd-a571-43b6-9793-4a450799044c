<!DOCTYPE html>

<html>
  <head>
  <meta charset='utf-8'>
  <title>TestNG reports</title>

    <link type="text/css" href="testng-reports.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery-1.7.1.min.js"></script>
    <script type="text/javascript" src="testng-reports.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type='text/javascript'>
      google.load('visualization', '1', {packages:['table']});
      google.setOnLoadCallback(drawTable);
      var suiteTableInitFunctions = new Array();
      var suiteTableData = new Array();
    </script>
    <!--
      <script type="text/javascript" src="jquery-ui/js/jquery-ui-1.8.16.custom.min.js"></script>
     -->
  </head>

  <body>
    <div class="top-banner-root">
      <span class="top-banner-title-font">Test results</span>
      <br/>
      <span class="top-banner-font-1">1 suite</span>
    </div> <!-- top-banner-root -->
    <div class="navigator-root">
      <div class="navigator-suite-header">
        <span>All suites</span>
        <a href="#" class="collapse-all-link" title="Collapse/expand all the suites">
          <img class="collapse-all-icon" src="collapseall.gif">
          </img> <!-- collapse-all-icon -->
        </a> <!-- collapse-all-link -->
      </div> <!-- navigator-suite-header -->
      <div class="suite">
        <div class="rounded-window">
          <div class="suite-header light-rounded-window-top">
            <a href="#" class="navigator-link" panel-name="suite-Suite">
              <span class="suite-name border-passed">Suite</span>
            </a> <!-- navigator-link -->
          </div> <!-- suite-header light-rounded-window-top -->
          <div class="navigator-suite-content">
            <div class="suite-section-title">
              <span>Info</span>
            </div> <!-- suite-section-title -->
            <div class="suite-section-content">
              <ul>
                <li>
                  <a href="#" class="navigator-link " panel-name="test-xml-Suite">
                    <span>androidSanity.xml</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="testlist-Suite">
                    <span class="test-stats">1 test</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="group-Suite">
                    <span>2 groups</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="times-Suite">
                    <span>Times</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="reporter-Suite">
                    <span>Reporter output</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="ignored-methods-Suite">
                    <span>Ignored methods</span>
                  </a> <!-- navigator-link  -->
                </li>
                <li>
                  <a href="#" class="navigator-link " panel-name="chronological-Suite">
                    <span>Chronological view</span>
                  </a> <!-- navigator-link  -->
                </li>
              </ul>
            </div> <!-- suite-section-content -->
            <div class="result-section">
              <div class="suite-section-title">
                <span>Results</span>
              </div> <!-- suite-section-title -->
              <div class="suite-section-content">
                <ul>
                  <li>
                    <span class="method-stats">1 method,   1 passed</span>
                  </li>
                  <li>
                    <span class="method-list-title passed">Passed methods</span>
                    <span class="show-or-hide-methods passed">
                      <a href="#" panel-name="suite-Suite" class="hide-methods passed suite-Suite"> (hide)</a> <!-- hide-methods passed suite-Suite -->
                      <a href="#" panel-name="suite-Suite" class="show-methods passed suite-Suite"> (show)</a> <!-- show-methods passed suite-Suite -->
                    </span>
                    <div class="method-list-content passed suite-Suite">
                      <span>
                        <img width="3%" src="passed.png"/>
                        <a href="#" class="method navigator-link" panel-name="suite-Suite" title="com.qa.indiaAndroid.TestCase" hash-for-method="deleteAddress(sunglass, Regular sunglasses)">deleteAddress(sunglass, Regular sunglasses)</a> <!-- method navigator-link -->
                      </span>
                      <br/>
                    </div> <!-- method-list-content passed suite-Suite -->
                  </li>
                </ul>
              </div> <!-- suite-section-content -->
            </div> <!-- result-section -->
          </div> <!-- navigator-suite-content -->
        </div> <!-- rounded-window -->
      </div> <!-- suite -->
    </div> <!-- navigator-root -->
    <div class="wrapper">
      <div class="main-panel-root">
        <div panel-name="suite-Suite" class="panel Suite">
          <div class="suite-Suite-class-passed">
            <div class="main-panel-header rounded-window-top">
              <img src="passed.png"/>
              <span class="class-name">com.qa.indiaAndroid.TestCase</span>
            </div> <!-- main-panel-header rounded-window-top -->
            <div class="main-panel-content rounded-window-bottom">
              <div class="method">
                <div class="method-content">
                  <a name="deleteAddress(sunglass, Regular sunglasses)">
                  </a> <!-- deleteAddress(sunglass, Regular sunglasses) -->
                  <span class="method-name">deleteAddress</span>
                  <span class="parameters">(sunglass, Regular sunglasses)</span>
                </div> <!-- method-content -->
              </div> <!-- method -->
            </div> <!-- main-panel-content rounded-window-bottom -->
          </div> <!-- suite-Suite-class-passed -->
        </div> <!-- panel Suite -->
        <div panel-name="test-xml-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">/Users/<USER>/Library/Lenskart-headless/android-pos-automation/src/test/resources/xml/androidSanity.xml</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <pre>
&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
&lt;!DOCTYPE suite SYSTEM &quot;http://testng.org/testng-1.0.dtd&quot;&gt;
&lt;suite name=&quot;Suite&quot; parallel=&quot;tests&quot;&gt;
  &lt;listeners&gt;
    &lt;listener class-name=&quot;com.qa.listeners.TestListener&quot;/&gt;
  &lt;/listeners&gt;
  &lt;test thread-count=&quot;5&quot; name=&quot;Test1&quot; parallel=&quot;tests&quot;&gt;
    &lt;parameter name=&quot;country&quot; value=&quot;IN&quot;/&gt;
    &lt;parameter name=&quot;loginUser&quot; value=&quot;androidUser1&quot;/&gt;
    &lt;parameter name=&quot;automationRunningPlatform&quot; value=&quot;browserStackAndroid&quot;/&gt;
    &lt;parameter name=&quot;platformVersion&quot; value=&quot;11.0&quot;/&gt;
    &lt;parameter name=&quot;platformName&quot; value=&quot;android&quot;/&gt;
    &lt;parameter name=&quot;deviceName&quot; value=&quot;OnePlus 9&quot;/&gt;
    &lt;groups&gt;
      &lt;run&gt;
        &lt;include name=&quot;regression&quot;/&gt;
      &lt;/run&gt;
    &lt;/groups&gt;
    &lt;classes&gt;
      &lt;class name=&quot;com.qa.indiaAndroid.TestCase&quot;/&gt;
    &lt;/classes&gt;
  &lt;/test&gt; &lt;!-- Test1 --&gt;
&lt;/suite&gt; &lt;!-- Suite --&gt;
            </pre>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="testlist-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Tests for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <ul>
              <li>
                <span class="test-name">Test1 (1 class)</span>
              </li>
            </ul>
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="group-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Groups for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="test-group">
              <span class="test-group-name">regression</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">deleteAddress</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
            <div class="test-group">
              <span class="test-group-name">sanity</span>
              <br/>
              <div class="method-in-group">
                <span class="method-in-group-name">deleteAddress</span>
                <br/>
              </div> <!-- method-in-group -->
            </div> <!-- test-group -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="times-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Times for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="times-div">
              <script type="text/javascript">
suiteTableInitFunctions.push('tableData_Suite');
function tableData_Suite() {
var data = new google.visualization.DataTable();
data.addColumn('number', 'Number');
data.addColumn('string', 'Method');
data.addColumn('string', 'Class');
data.addColumn('number', 'Time (ms)');
data.addRows(1);
data.setCell(0, 0, 0)
data.setCell(0, 1, 'deleteAddress')
data.setCell(0, 2, 'com.qa.indiaAndroid.TestCase')
data.setCell(0, 3, 1501);
window.suiteTableData['Suite']= { tableData: data, tableDiv: 'times-div-Suite'}
return data;
}
              </script>
              <span class="suite-total-time">Total running time: 1 seconds</span>
              <div id="times-div-Suite">
              </div> <!-- times-div-Suite -->
            </div> <!-- times-div -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="reporter-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Reporter output for Suite</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="ignored-methods-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">0 ignored methods</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
        <div panel-name="chronological-Suite" class="panel">
          <div class="main-panel-header rounded-window-top">
            <span class="header-content">Methods in chronological order</span>
          </div> <!-- main-panel-header rounded-window-top -->
          <div class="main-panel-content rounded-window-bottom">
            <div class="chronological-class">
              <div class="chronological-class-name">com.qa.indiaAndroid.TestCase</div> <!-- chronological-class-name -->
              <div class="configuration-test before">
                <span class="method-name">beforeClass(browserStackAndroid, android, null, OnePlus 9, 11.0, IN, null, androidUser1)</span>
                <span class="method-start">0 ms</span>
              </div> <!-- configuration-test before -->
              <div class="configuration-class before">
                <span class="method-name">beforeClass</span>
                <span class="method-start">35833 ms</span>
              </div> <!-- configuration-class before -->
              <div class="configuration-method before">
                <span class="method-name">beforeMethod(public void com.qa.indiaAndroid.TestCase.deleteAddress(java.lang.String,java.lang.String))</span>
                <span class="method-start">36012 ms</span>
              </div> <!-- configuration-method before -->
              <div class="test-method">
                <span class="method-name">deleteAddress(sunglass, Regular sunglasses)</span>
                <span class="method-start">36013 ms</span>
              </div> <!-- test-method -->
              <div class="configuration-class after">
                <span class="method-name">afterMethod</span>
                <span class="method-start">37516 ms</span>
              </div> <!-- configuration-class after -->
              <div class="configuration-test after">
                <span class="method-name">afterClass</span>
                <span class="method-start">37673 ms</span>
              </div> <!-- configuration-test after -->
          </div> <!-- main-panel-content rounded-window-bottom -->
        </div> <!-- panel -->
      </div> <!-- main-panel-root -->
    </div> <!-- wrapper -->
  </body>
</html>
