<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitXMLReporter -->
<testsuite hostname="R-Ragul" ignored="0" name="Test" tests="2" failures="2" timestamp="08 Oct 2025 12:14:38 GMT" time="0.735" errors="0">
  <testcase name="@AfterClass afterMethod" time="0.0" classname="com.qa.indiaAndroid.TestCase">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.qa.BaseTest.closeApp(BaseTest.java:815)
at com.qa.indiaAndroid.TestCase.afterMethod(TestCase.java:75)
... Removed 26 stack frames]]>
    </failure>
  </testcase> <!-- @AfterClass afterMethod -->
  <testcase name="@AfterTest afterClass" time="0.0" classname="com.qa.BaseTest">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.qa.BaseTest.afterClass(BaseTest.java:129)
... Removed 24 stack frames]]>
    </failure>
  </testcase> <!-- @AfterTest afterClass -->
  <testcase name="deleteAddress" time="0.098" classname="com.qa.indiaAndroid.TestCase">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.qa.BaseTest.launchApp(BaseTest.java:795)
at com.qa.indiaAndroid.TestCase.deleteAddress(TestCase.java:66)
... Removed 25 stack frames]]>
    </failure>
  </testcase> <!-- deleteAddress -->
  <testcase name="deleteAddress" time="0.111" classname="com.qa.indiaAndroid.TestCase">
    <failure type="java.lang.NullPointerException">
      <![CDATA[java.lang.NullPointerException
at com.qa.listeners.TestListener.onTestFailure(TestListener.java:47)
... Removed 22 stack frames]]>
    </failure>
  </testcase> <!-- deleteAddress -->
</testsuite> <!-- Test -->
