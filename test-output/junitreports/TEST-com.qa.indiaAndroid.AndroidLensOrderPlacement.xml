<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by org.testng.reporters.JUnitReportReporter -->
<testsuite skipped="0" hostname="R-Ragul" name="com.qa.indiaAndroid.AndroidLensOrderPlacement" tests="1" failures="1" timestamp="08 Oct 2025 12:05:12 GMT" time="23.549" errors="0">
  <testcase name="eyeglassesComputorGlasses" time="23.549" classname="com.qa.indiaAndroid.AndroidLensOrderPlacement">
    <failure type="java.lang.AssertionError" message="Home page not loaded expected [true] but found [false]">
      <![CDATA[java.lang.AssertionError: Home page not loaded expected [true] but found [false]
at org.testng.Assert.fail(Assert.java:96)
at org.testng.Assert.failNotEquals(Assert.java:776)
at org.testng.Assert.assertTrue(Assert.java:44)
at com.qa.appPages.AppHomePage.clickOnCartIcon(AppHomePage.java:187)
at com.qa.appPages.AppLoginPage.loginMobileNumberAndDoEmptyCart(AppLoginPage.java:761)
at com.qa.indiaAndroid.AndroidLensOrderPlacement.eyeglassesComputorGlasses(AndroidLensOrderPlacement.java:87)
at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
at java.lang.reflect.Method.invoke(Method.java:498)
at org.testng.internal.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:124)
at org.testng.internal.Invoker.invokeMethod(Invoker.java:583)
at org.testng.internal.Invoker.invokeTestMethod(Invoker.java:719)
at org.testng.internal.Invoker.invokeTestMethods(Invoker.java:989)
at org.testng.internal.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:125)
at org.testng.internal.TestMethodWorker.run(TestMethodWorker.java:109)
at org.testng.TestRunner.privateRun(TestRunner.java:648)
at org.testng.TestRunner.run(TestRunner.java:505)
at org.testng.SuiteRunner.runTest(SuiteRunner.java:455)
at org.testng.SuiteRunner.access$000(SuiteRunner.java:40)
at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:489)
at org.testng.internal.thread.ThreadUtil$1.call(ThreadUtil.java:52)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:750)
]]>
    </failure>
  </testcase> <!-- eyeglassesComputorGlasses -->
</testsuite> <!-- com.qa.indiaAndroid.AndroidLensOrderPlacement -->
