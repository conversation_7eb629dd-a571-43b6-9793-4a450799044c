<?xml version="1.0" encoding="UTF-8"?>
<testng-results skipped="0" failed="0" ignored="0" total="1" passed="1">
  <reporter-output>
  </reporter-output>
  <suite name="Suite" duration-ms="39000" started-at="2025-10-08T12:15:12Z" finished-at="2025-10-08T12:15:51Z">
    <groups>
      <group name="regression">
        <method signature="TestCase.deleteAddress(java.lang.String, java.lang.String)[pri:3, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="deleteAddress" class="com.qa.indiaAndroid.TestCase"/>
      </group> <!-- regression -->
      <group name="sanity">
        <method signature="TestCase.deleteAddress(java.lang.String, java.lang.String)[pri:3, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="deleteAddress" class="com.qa.indiaAndroid.TestCase"/>
      </group> <!-- sanity -->
    </groups>
    <test name="Test1" duration-ms="39000" started-at="2025-10-08T12:15:12Z" finished-at="2025-10-08T12:15:51Z">
      <class name="com.qa.indiaAndroid.TestCase">
        <test-method status="PASS" signature="beforeClass(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="beforeClass" is-config="true" duration-ms="35829" started-at="2025-10-08T12:15:12Z" finished-at="2025-10-08T12:15:48Z">
          <params>
            <param index="0">
              <value>
                <![CDATA[browserStackAndroid]]>
              </value>
            </param>
            <param index="1">
              <value>
                <![CDATA[android]]>
              </value>
            </param>
            <param index="2">
              <value is-null="true"/>
            </param>
            <param index="3">
              <value>
                <![CDATA[OnePlus 9]]>
              </value>
            </param>
            <param index="4">
              <value>
                <![CDATA[11.0]]>
              </value>
            </param>
            <param index="5">
              <value>
                <![CDATA[IN]]>
              </value>
            </param>
            <param index="6">
              <value is-null="true"/>
            </param>
            <param index="7">
              <value>
                <![CDATA[androidUser1]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- beforeClass -->
        <test-method status="PASS" signature="beforeClass()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="beforeClass" is-config="true" duration-ms="171" started-at="2025-10-08T12:15:48Z" finished-at="2025-10-08T12:15:48Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- beforeClass -->
        <test-method status="PASS" signature="beforeMethod(java.lang.reflect.Method)[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="beforeMethod" is-config="true" duration-ms="1" started-at="2025-10-08T12:15:48Z" finished-at="2025-10-08T12:15:48Z">
          <params>
            <param index="0">
              <value>
                <![CDATA[public void com.qa.indiaAndroid.TestCase.deleteAddress(java.lang.String,java.lang.String)]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- beforeMethod -->
        <test-method status="PASS" signature="deleteAddress(java.lang.String, java.lang.String)[pri:3, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="deleteAddress" duration-ms="1501" started-at="2025-10-08T12:15:48Z" data-provider="sunglassWithoutPower" finished-at="2025-10-08T12:15:50Z">
          <params>
            <param index="0">
              <value>
                <![CDATA[sunglass]]>
              </value>
            </param>
            <param index="1">
              <value>
                <![CDATA[Regular sunglasses]]>
              </value>
            </param>
          </params>
          <reporter-output>
          </reporter-output>
        </test-method> <!-- deleteAddress -->
        <test-method status="PASS" signature="afterMethod()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="afterMethod" is-config="true" duration-ms="156" started-at="2025-10-08T12:15:50Z" finished-at="2025-10-08T12:15:50Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- afterMethod -->
        <test-method status="PASS" signature="afterClass()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]" name="afterClass" is-config="true" duration-ms="1322" started-at="2025-10-08T12:15:50Z" finished-at="2025-10-08T12:15:51Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- afterClass -->
      </class> <!-- com.qa.indiaAndroid.TestCase -->
    </test> <!-- Test1 -->
  </suite> <!-- Suite -->
</testng-results>
