<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Failed suite [Suite]" parallel="tests">
  <listeners>
    <listener class-name="com.qa.listeners.TestListener"/>
  </listeners>
  <test thread-count="5" name="Test1(failed)" parallel="tests">
    <parameter name="country" value="IN"/>
    <parameter name="loginUser" value="androidUser1"/>
    <parameter name="automationRunningPlatform" value="browserStackAndroid"/>
    <parameter name="platformVersion" value="11.0"/>
    <parameter name="platformName" value="android"/>
    <parameter name="deviceName" value="OnePlus 9"/>
    <groups>
      <run>
        <include name="regression"/>
      </run>
    </groups>
    <classes>
      <class name="com.qa.indiaAndroid.AndroidLensOrderPlacement">
        <methods>
          <include name="afterClass"/>
          <include name="beforeClass"/>
          <include name="eyeglassesComputorGlasses" invocation-numbers="0"/>
          <include name="beforeClass"/>
          <include name="beforeMethod"/>
          <include name="afterMethod"/>
        </methods>
      </class> <!-- com.qa.indiaAndroid.AndroidLensOrderPlacement -->
    </classes>
  </test> <!-- Test1(failed) -->
</suite> <!-- Failed suite [Suite] -->
