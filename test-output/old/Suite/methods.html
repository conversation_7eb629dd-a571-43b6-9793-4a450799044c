<h2>Methods run, sorted chronologically</h2><h3>&gt;&gt; means before, &lt;&lt; means after</h3><p/><br/><em>Suite</em><p/><small><i>(Hover the method name to see the test class name)</i></small><p/>
<table border="1">
<tr><th>Time</th><th>Delta (ms)</th><th>Suite<br>configuration</th><th>Test<br>configuration</th><th>Class<br>configuration</th><th>Groups<br>configuration</th><th>Method<br>configuration</th><th>Test<br>method</th><th>Thread</th><th>Instances</th></tr>
<tr bgcolor="c296f6">  <td>25/10/08 17:45:12</td>   <td>0</td> <td>&nbsp;</td><td title="&gt;&gt;BaseTest.beforeClass(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
<tr bgcolor="afbea5">  <td>25/10/08 17:45:48</td>   <td>35827</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestCase.beforeClass()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">&gt;&gt;beforeClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
<tr bgcolor="afbea5">  <td>25/10/08 17:45:48</td>   <td>36005</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="&gt;&gt;TestCase.beforeMethod(java.lang.reflect.Method)[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">&gt;&gt;beforeMethod</td> 
<td>&nbsp;</td>  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
<tr bgcolor="afbea5">  <td>25/10/08 17:45:48</td>   <td>36006</td> <td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td title="TestCase.deleteAddress(java.lang.String, java.lang.String)[pri:3, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">deleteAddress</td> 
  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
<tr bgcolor="afbea5">  <td>25/10/08 17:45:50</td>   <td>37509</td> <td>&nbsp;</td><td>&nbsp;</td><td title="&lt;&lt;TestCase.afterMethod()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">&lt;&lt;afterMethod</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
<tr bgcolor="c296f6">  <td>25/10/08 17:45:50</td>   <td>37666</td> <td>&nbsp;</td><td title="&lt;&lt;BaseTest.afterClass()[pri:0, instance:com.qa.indiaAndroid.TestCase@4ddbbdf8]">&lt;&lt;afterClass</td> 
<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td>  <td>TestNG-tests-1@547314766</td>   <td></td> </tr>
</table>
