# Ignore everything by default
*

# Allow src directory and its contents
!src/
!src/**

# Allow feature files (common extensions for feature additions)
!*.feature
!*feature*
!feature*/
!features/
!features/**

# Allow essential project files
!pom.xml
!README.md
!.gitignore

# Allow configuration files that might be needed for features
!*.yml
!*.yaml
!*.json
!*.properties
!*.xml

# Maven specific ignores
target/
.mvn/
mvnw
mvnw.cmd

# IDE specific ignores
.idea/
.vscode/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/

# Test outputs and reports
test-output/
ExtendReport/
Screenshots/
logs/
log/

# OS specific ignores
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.swp
*.swo
*~
